@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    box-sizing: border-box;
  }

  body {
    @apply bg-gradient-to-br from-slate-50 to-blue-50 font-sans antialiased;
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Glass morphism utilities */
  .glass {
    @apply bg-white/10 backdrop-blur-md border border-white/20;
  }

  .glass-dark {
    @apply bg-black/10 backdrop-blur-md border border-white/10;
  }

  /* Modern button styles */
  .btn-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-secondary-500 to-secondary-600 hover:from-secondary-600 hover:to-secondary-700 text-white font-semibold py-3 px-6 rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200;
  }

  .btn-glass {
    @apply glass hover:bg-white/20 text-white font-semibold py-3 px-6 rounded-xl shadow-glass hover:shadow-xl transform hover:scale-105 transition-all duration-200;
  }

  /* Modern card styles */
  .card-modern {
    @apply bg-white/80 backdrop-blur-sm border border-white/20 rounded-2xl shadow-glass hover:shadow-xl transition-all duration-300;
  }

  .card-glass {
    @apply glass rounded-2xl shadow-glass hover:shadow-xl transition-all duration-300;
  }

  /* Input styles */
  .input-modern {
    @apply w-full px-4 py-3 bg-white/50 backdrop-blur-sm border border-white/30 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
  }

  /* Gradient text */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }

  .text-gradient-accent {
    @apply bg-gradient-to-r from-accent-500 to-secondary-500 bg-clip-text text-transparent;
  }

  /* Animated backgrounds */
  .bg-animated {
    @apply bg-gradient-to-br from-primary-400 via-secondary-500 to-accent-400 animate-gradient-xy;
    background-size: 400% 400%;
  }

  .bg-mesh {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 25%, #f093fb 50%, #f5576c 75%, #4facfe 100%);
    background-size: 400% 400%;
    animation: gradient-xy 15s ease infinite;
  }
}

@layer utilities {
  /* Custom scrollbar */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Smooth transitions */
  .transition-smooth {
    @apply transition-all duration-300 ease-in-out;
  }

  /* Hover lift effect */
  .hover-lift {
    @apply transform hover:scale-105 hover:-translate-y-1 transition-all duration-200;
  }

  /* Glow effects */
  .glow-primary {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .glow-secondary {
    box-shadow: 0 0 20px rgba(217, 70, 239, 0.3);
  }

  /* Text shadows */
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
}